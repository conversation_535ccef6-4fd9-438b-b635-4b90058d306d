:root {
  --purple: var(
    --primary-light-color
  ); /* You can override this in index.css if needed */
  --orange: #ffe9d9;
  --green: #d6f5e8;
  --section-padding: 20px;
}

.dashboard-container {
  padding: var(--section-padding);
  background-color: var(--white);
  font-family: "Segoe UI", sans-serif;
}

.stats-container {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.stats-card {
  flex: 1;
  min-width: 150px;
  border-radius: var(--border-radius);
  padding: 15px;
  background-color: var(--white);
  box-shadow: var(--box-shadow-light);
  text-align: center;
}

.stats-card h2 {
  margin: 0;
  font-size: 24px;
  color: var(--text-color);
}

.stats-card p {
  margin: 4px 0 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.stats-card.purple {
  background-color: var(--purple);
}

.stats-card.orange {
  background-color: var(--orange);
}

.stats-card.green {
  background-color: var(--green);
}

.section {
  margin-top: 40px;
  padding: 20px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  font-size: var(--basefont);
  color: var(--primary-text);
}

.section-header a {
  font-size: var(--smallfont);
  color: var(--btn-color);
  text-decoration: none;
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

th,
td {
  padding: 12px 10px;
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
}

.video-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-icon {
  color: var(--btn-color);
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 22px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  background-color: var(--light-gray);
  border-radius: 22px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: 0.4s;
}

.slider::before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 3px;
  bottom: 3px;
  background-color: var(--white);
  border-radius: 50%;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: var(--btn-color);
}

input:checked + .slider::before {
  transform: translateX(18px);
}

.slider.round {
  border-radius: 34px;
}

/* Bid-specific icon */
.bid-icon {
  font-size: 18px;
  color: var(--btn-color);
  cursor: pointer;
}
